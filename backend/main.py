"""FastAPI backend for BaliBlissed AI features.

This module provides API endpoints to handle AI-powered functionalities
such as itinerary suggestions, user query responses, and contact form analysis.
"""

import json
import logging
import os
import re
import time
from collections.abc import AsyncGenerator, Awaitable, Callable
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import Any, Optional

import google.generativeai as genai
from dotenv import load_dotenv
from fastapi import FastAPI, HTTPException, Request, Response, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr, Field, field_validator

# --- Constants ---
MAX_QUERY_LENGTH = 1000
MAX_MESSAGE_LENGTH = 2000
MAX_DESTINATION_LENGTH = 100
MAX_NAME_LENGTH = 100
MIN_TRIP_DURATION = 1
MAX_TRIP_DURATION = 365
MAX_INTERESTS_COUNT = 20
MIN_MESSAGE_LENGTH = 10

# Rate limiting constants
RATE_LIMIT_REQUESTS = 100
RATE_LIMIT_WINDOW = 3600  # 1 hour in seconds

# Response constants
DEFAULT_ERROR_MESSAGE = "An unexpected server error occurred."
ITINERARY_GENERATION_ERROR = "Failed to generate itinerary."
QUERY_PROCESSING_ERROR = "Failed to process query."
CONTACT_INQUIRY_ERROR = "Failed to process contact inquiry."

# --- Custom Exceptions ---


class AIGenerationError(Exception):
    """Raised when AI content generation fails."""

    def __init__(
        self,
        message: str,
        original_error: Optional[Exception] = None,
    ) -> None:
        """Initialize AIGenerationError.

        Args:
            message: Error message describing the failure
            original_error: The original exception that caused this error

        """
        super().__init__(message)
        self.original_error = original_error


class ItineraryGenerationError(AIGenerationError):
    """Raised when AI itinerary generation fails."""


class QueryProcessingError(AIGenerationError):
    """Raised when AI query processing fails."""


class ContactAnalysisError(AIGenerationError):
    """Raised when AI contact analysis fails."""


# --- Environment and Logging Configuration ---

# Load environment variables from a .env file for secure key management
load_dotenv()

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log")
        if os.getenv("LOG_TO_FILE")
        else logging.NullHandler(),
    ],
)
logger = logging.getLogger(__name__)

# Validate required environment variables
REQUIRED_ENV_VARS = ["GEMINI_API_KEY"]
missing_vars = [var for var in REQUIRED_ENV_VARS if not os.getenv(var)]
if missing_vars:
    logger.exception(f"Missing required environment variables: {missing_vars}")
    mssg = f"Missing required environment variables: {missing_vars}"
    raise ValueError(mssg)

# Configure Google Generative AI
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)

# AI Model Configuration
GEMINI_MODEL = "gemini-2.0-flash"
GENERATION_CONFIG = {
    "temperature": 0.7,
    "top_p": 0.8,
    "top_k": 40,
    "max_output_tokens": 2048,
}

# Safety settings for content generation
SAFETY_SETTINGS = [
    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {
        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE",
    },
    {
        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE",
    },
]

# --- Application Lifecycle Management ---


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Manage application startup and shutdown events."""
    # Startup
    logger.info("Starting BaliBlissed AI Backend...")
    yield
    # Shutdown
    logger.info("Shutting down BaliBlissed AI Backend...")


# --- Application Initialization ---

app = FastAPI(
    title="BaliBlissed AI Backend",
    description="Provides AI-powered services for the BaliBlissed Next.js application.",
    version="1.0.0",
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
    lifespan=lifespan,
)

# --- Security Headers Middleware ---


@app.middleware("http")
async def add_security_headers(
    request: Request,
    call_next: Callable[[Request], Awaitable[Response]],
) -> Response:
    """Add security headers to all responses."""
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = (
        "max-age=31536000; includeSubDomains"
    )
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    return response


# --- Request Logging Middleware ---


@app.middleware("http")
async def log_requests(
    request: Request,
    call_next: Callable[[Request], Awaitable[Response]],
) -> Response:
    """Log all incoming requests with timing information."""
    start_time: float = time.time()
    client_ip: str = request.client.host if request.client else "unknown"

    logger.info(f"Request: {request.method} {request.url.path} from {client_ip}")

    response = await call_next(request)

    process_time: float = time.time() - start_time
    logger.info(
        f"Response: {response.status_code} for {request.method} {request.url.path} "
        f"in {process_time:.4f}s",
    )

    return response


# --- CORS Configuration ---

# Determine allowed origins based on environment
allowed_origins: list[str] = [
    "http://localhost:3000",  # Next.js development
    "http://127.0.0.1:3000",
]

# Add production origins if specified
if production_origin := os.getenv("PRODUCTION_FRONTEND_URL"):
    allowed_origins.append(production_origin)

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["X-Request-ID"],
)

# --- Compression Middleware ---

app.add_middleware(GZipMiddleware, minimum_size=1000)

# --- Pydantic Models for Data Validation ---


class ItineraryRequest(BaseModel):
    """Model for travel itinerary generation requests."""

    destination: str = Field(
        ...,
        min_length=1,
        max_length=MAX_DESTINATION_LENGTH,
        description="The travel destination",
        example="Bali, Indonesia",
    )
    duration: int = Field(
        ...,
        ge=MIN_TRIP_DURATION,
        le=MAX_TRIP_DURATION,
        description="The duration of the trip in days",
        example=7,
    )
    interests: list[str] = Field(
        ...,
        min_length=1,
        max_length=MAX_INTERESTS_COUNT,
        description="A list of traveler's interests",
        example=["beaches", "temples", "food", "culture"],
    )

    @field_validator("destination")
    @classmethod
    def validate_destination(cls, v: str) -> str:
        """Validate and sanitize destination input."""
        if not v or not v.strip():
            mssg = "Destination cannot be empty"
            raise ValueError(mssg)
        # Remove potentially harmful characters
        sanitized = re.sub(r'[<>"\']', "", v.strip())
        if len(sanitized) < 1:
            mssg = "Destination must contain valid characters"
            raise ValueError(mssg)
        return sanitized

    @field_validator("interests")
    @classmethod
    def validate_interests(cls, v: list[str]) -> list[str]:
        """Validate and sanitize interests list."""
        if not v:
            mssg = "At least one interest must be provided"
            raise ValueError(mssg)

        sanitized_interests = []
        for interest in v:
            if isinstance(interest, str) and interest.strip():
                # Remove potentially harmful characters and limit length
                sanitized = re.sub(r'[<>"\']', "", interest.strip())[:50]
                if sanitized:
                    sanitized_interests.append(sanitized)

        if not sanitized_interests:
            mssg = "At least one valid interest must be provided"
            raise ValueError(mssg)

        return sanitized_interests[:MAX_INTERESTS_COUNT]


class ChatMessage(BaseModel):
    """Model for individual chat messages in conversation history."""

    role: str = Field(
        ...,
        description="The role of the message sender",
        example="user",
    )
    parts: list[dict[str, str]] = Field(
        ...,
        description="The message content parts",
        example=[{"text": "Hello, how are you?"}],
    )

    @field_validator("role")
    @classmethod
    def validate_role(cls, v: str) -> str:
        """Validate message role."""
        allowed_roles = {"user", "assistant", "system"}
        if v not in allowed_roles:
            mssg = f"Role must be one of: {allowed_roles}"
            raise ValueError(mssg)
        return v


class QueryRequest(BaseModel):
    """Model for user query requests."""

    query: str = Field(
        ...,
        min_length=1,
        max_length=MAX_QUERY_LENGTH,
        description="The user's query",
        example="What are the best beaches in Bali?",
    )
    history: list[ChatMessage] = Field(
        default=[],
        max_length=50,  # Limit conversation history
        description="The chat history",
    )

    @field_validator("query")
    @classmethod
    def validate_query(cls, v: str) -> str:
        """Validate and sanitize query input."""
        if not v or not v.strip():
            mssg = "Query cannot be empty"
            raise ValueError(mssg)
        # Remove potentially harmful characters
        sanitized = re.sub(r'[<>"\']', "", v.strip())
        if len(sanitized) < 1:
            mssg = "Query must contain valid characters"
            raise ValueError(mssg)
        return sanitized


class ContactInquiryRequest(BaseModel):
    """Model for contact form submissions."""

    name: str = Field(
        ...,
        min_length=1,
        max_length=MAX_NAME_LENGTH,
        description="The user's name",
        example="John Doe",
    )
    email: EmailStr = Field(
        ...,
        description="The user's email address",
        example="<EMAIL>",
    )
    message: str = Field(
        ...,
        min_length=MIN_MESSAGE_LENGTH,
        max_length=MAX_MESSAGE_LENGTH,
        description="The user's message",
        example="I would like to know more about your services.",
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate and sanitize name input."""
        if not v or not v.strip():
            mssg = "Name cannot be empty"
            raise ValueError(mssg)
        # Remove potentially harmful characters
        sanitized = re.sub(r'[<>"\']', "", v.strip())
        if len(sanitized) < 1:
            mssg = "Name must contain valid characters"
            raise ValueError(mssg)
        return sanitized

    @field_validator("message")
    @classmethod
    def validate_message(cls, v: str) -> str:
        """Validate and sanitize message input."""
        if not v or not v.strip():
            mssg = "Message cannot be empty"
            raise ValueError(mssg)
        # Remove potentially harmful characters but preserve basic formatting
        sanitized = re.sub(r"[<>]", "", v.strip())
        if len(sanitized) < MIN_MESSAGE_LENGTH:
            mssg = f"Message must be at least {MIN_MESSAGE_LENGTH} characters long"
            raise ValueError(
                mssg,
            )
        return sanitized


# --- Response Models ---


class ErrorResponse(BaseModel):
    """Standard error response model."""

    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Additional error details")
    timestamp: str = Field(..., description="Error timestamp")


class ItineraryResponse(BaseModel):
    """Response model for itinerary generation."""

    itinerary: str = Field(..., description="Generated travel itinerary")


class QueryResponse(BaseModel):
    """Response model for query processing."""

    answer: str = Field(..., description="AI-generated response to the query")


class ContactAnalysisResponse(BaseModel):
    """Response model for contact inquiry analysis."""

    analysis: dict[str, str] = Field(..., description="Analysis of the contact inquiry")


# --- AI Service Functions ---


def create_error_response(message: str, detail: Optional[str] = None) -> dict[str, Any]:
    """Create a standardized error response."""
    return {
        "error": message,
        "detail": detail,
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }


def create_itinerary_prompt(request: ItineraryRequest) -> str:
    """Create a detailed prompt for itinerary generation.

    Args: request: The itinerary request containing destination, duration, and interests.

    Returns: A formatted prompt string for the AI model.
    """
    interests_text = ", ".join(request.interests)

    return f"""
You are an expert travel planner specializing in Bali, Indonesia. Create a detailed {request.duration}-day travel itinerary for {request.destination}.

**Traveler Interests:** {interests_text}
**Duration:** {request.duration} days

Please provide:
1. **Daily Schedule**: Detailed day-by-day activities
2. **Accommodation Recommendations**: Specific hotels/villas with brief descriptions
3. **Restaurant Suggestions**: Local cuisine recommendations for each area
4. **Transportation**: How to get between locations
5. **Cultural Tips**: Local customs and etiquette
6. **Budget Estimates**: Approximate costs in USD
7. **Best Times to Visit**: Optimal timing for activities
8. **Hidden Gems**: Lesser-known attractions based on interests

Format the response in clear markdown with headers and bullet points. Focus on authentic Balinese experiences while considering the traveler's specific interests: {interests_text}.

Make the itinerary practical, culturally respectful, and tailored to create memorable experiences in Bali.
"""


def format_chat_history(history: list[ChatMessage]) -> list[dict[str, str]]:
    """Format chat history for Gemini API.

    Args: history: List of chat messages from the conversation.

    Returns: Formatted history for Gemini chat API.
    """
    formatted_history = []

    for message in history:
        # Convert role to Gemini format
        role = "user" if message.role == "user" else "model"

        content = "".join(
            part["text"] + " " for part in message.parts if "text" in part
        )
        if content.strip():
            formatted_history.append(
                {"role": role, "parts": [{"text": content.strip()}]},
            )

    return formatted_history


def create_analysis_prompt(message: str) -> str:
    """Create a prompt for contact inquiry analysis.

    Args: message: The contact message to analyze.

    Returns:  A formatted prompt string for analysis.
    """
    return f"""
Analyze the following customer inquiry for a Bali travel service company. Provide a structured analysis in JSON format.

**Customer Message:** "{message}"

Please analyze and provide:
1. **Summary**: A brief 1-2 sentence summary of the inquiry
2. **Category**: Classify into one of these categories:
   - "Booking Inquiry" - Questions about reservations, availability
   - "Itinerary Planning" - Requests for travel planning assistance
   - "General Information" - Questions about Bali, services, policies
   - "Support Request" - Issues, complaints, or assistance needed
   - "Pricing Inquiry" - Questions about costs, packages, pricing
   - "Feedback" - Reviews, testimonials, suggestions

3. **Urgency**: Rate as "High", "Medium", or "Low"
4. **Suggested Response**: A professional, helpful response template
5. **Required Action**: What the team should do next
6. **Keywords**: Key topics mentioned in the message

Respond ONLY with a valid JSON object in this exact format:
{{
    "summary": "Brief summary here",
    "category": "Category name",
    "urgency": "Urgency level",
    "suggested_reply": "Professional response template",
    "required_action": "Next steps for the team",
    "keywords": ["keyword1", "keyword2", "keyword3"]
}}
"""


async def generate_itinerary_with_ai(request: ItineraryRequest) -> str:
    """Generate a travel itinerary using Google Gemini AI.

    Args: request: The itinerary request containing destination, duration, and interests.

    Returns: AI-generated travel itinerary.

    Raises: ItineraryGenerationError: If AI generation fails.
    """

    def _raise_empty_response_error() -> None:
        """Raise ValueError for empty AI response."""
        mssg = "AI model returned empty response"
        raise ValueError(mssg)

    try:
        model = genai.GenerativeModel(
            model_name=GEMINI_MODEL,
            generation_config=GENERATION_CONFIG,
            safety_settings=SAFETY_SETTINGS,
        )

        prompt = create_itinerary_prompt(request)
        response = await model.generate_content_async(prompt)

        if not response.text:
            _raise_empty_response_error()
        else:
            return response.text

    except Exception as e:
        logger.exception("AI itinerary generation failed")
        mssg = f"Failed to generate itinerary: {str(e)}"
        raise ItineraryGenerationError(mssg, e) from e


async def process_query_with_ai(request: QueryRequest) -> str:
    """Process user query using Google Gemini AI with conversation context.

    Args: request: The query request containing the user's query and history.

    Returns: AI-generated response to the query.

    Raises: QueryProcessingError: If AI processing fails.
    """

    def _raise_empty_response_error() -> None:
        """Raise ValueError for empty AI response."""
        mssg = "AI model returned empty response"
        raise ValueError(mssg)

    try:
        model = genai.GenerativeModel(
            model_name=GEMINI_MODEL,
            generation_config=GENERATION_CONFIG,
            safety_settings=SAFETY_SETTINGS,
        )

        # Format conversation history
        history = format_chat_history(request.history)

        if history:
            # Continue existing conversation
            chat = model.start_chat(history=history)
            response = await chat.send_message_async(request.query)
        else:
            # Create system prompt for Bali travel assistant
            system_prompt = """
You are a knowledgeable Bali travel assistant. You help travelers with:
- Travel planning and itinerary suggestions
- Information about Bali's attractions, culture, and customs
- Accommodation and restaurant recommendations
- Transportation and logistics advice
- Cultural etiquette and local tips
- Weather and seasonal information

Always provide helpful, accurate, and culturally respectful information about Bali.
Be friendly, professional, and focus on creating memorable travel experiences.
"""

            # Start new conversation with system context
            full_prompt = f"{system_prompt}\n\nUser Question: {request.query}"
            response = await model.generate_content_async(full_prompt)

        if not response.text:
            _raise_empty_response_error()
        else:
            return response.text

    except Exception as e:
        logger.exception("AI query processing failed")
        mssg = f"Failed to process query: {str(e)}"
        raise QueryProcessingError(mssg, e) from e


async def analyze_contact_with_ai(request: ContactInquiryRequest) -> dict[str, str]:
    """Analyze contact inquiry using Google Gemini AI.

    Args: request: The contact inquiry request containing name, email, and message.

    Returns: AI-generated analysis of the contact inquiry.

    Raises: ContactAnalysisError: If AI analysis fails.
    """

    def _raise_empty_response_error() -> None:
        """Raise ValueError for empty AI response."""
        mssg = "AI model returned empty response"
        raise ValueError(mssg)

    try:
        model = genai.GenerativeModel(
            model_name=GEMINI_MODEL,
            generation_config=GENERATION_CONFIG,
            safety_settings=SAFETY_SETTINGS,
        )

        prompt = create_analysis_prompt(request.message)
        response = await model.generate_content_async(prompt)

        if not response.text:
            _raise_empty_response_error()

        # Parse JSON response
        try:
            analysis = json.loads(response.text.strip())

            # Validate required fields
            required_fields = [
                "summary",
                "category",
                "urgency",
                "suggested_reply",
                "required_action",
                "keywords",
            ]
            for field in required_fields:
                if field not in analysis:
                    analysis[field] = "Not specified"

            # Convert keywords list to string if needed
            if isinstance(analysis.get("keywords"), list):
                analysis["keywords"] = ", ".join(analysis["keywords"])

            return {
                "summary": str(analysis["summary"]),
                "category": str(analysis["category"]),
                "urgency": str(analysis["urgency"]),
                "suggested_reply": str(analysis["suggested_reply"]),
                "required_action": str(analysis["required_action"]),
                "keywords": str(analysis["keywords"]),
            }

        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            logger.warning("Failed to parse AI analysis as JSON, using fallback")
            return {
                "summary": f"Analysis of inquiry from {request.name}",
                "category": "General Information",
                "urgency": "Medium",
                "suggested_reply": "Thank you for your inquiry. We'll review your message and get back to you soon.",
                "required_action": "Review and respond to customer inquiry",
                "keywords": "customer inquiry, travel, Bali",
            }

    except Exception as e:
        logger.exception("AI contact analysis failed")
        mssg = f"Failed to analyze contact inquiry: {str(e)}"
        raise ContactAnalysisError(mssg, e) from e


# --- API Endpoints ---


@app.get("/", response_model=dict[str, str])
async def read_root() -> dict[str, str]:
    """Health check endpoint to verify server status."""
    return {"status": "ok", "message": "Welcome to the BaliBlissed AI Backend!"}


@app.post(
    "/api/suggest-itinerary",
    response_model=ItineraryResponse,
    status_code=status.HTTP_200_OK,
    summary="Generate Travel Itinerary",
    description="Generate a personalized travel itinerary based on destination, duration, and interests.",
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request data"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
)
async def suggest_itinerary(request: ItineraryRequest) -> ItineraryResponse:
    """Generate a travel itinerary based on user preferences.

    This endpoint processes travel preferences and generates a customized itinerary
    using Google Gemini AI, specifically tailored for Bali travel experiences.

    Args: request: ItineraryRequest containing destination, duration, and interests

    Returns: ItineraryResponse: Generated travel itinerary

    Raises: ValueError: If input validation fails
        HTTPException: 400 for invalid input, 500 for server errors
    """
    logger.info(
        f"Processing itinerary request for {request.destination} "
        f"({request.duration} days, {len(request.interests)} interests)",
    )

    try:
        # Generate itinerary using AI
        itinerary_content = await generate_itinerary_with_ai(request)

        logger.info(f"Successfully generated itinerary for {request.destination}")
        return ItineraryResponse(itinerary=itinerary_content)

    except ValueError as e:
        logger.warning("Invalid input for itinerary generation")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.exception("Unexpected error in itinerary generation:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ITINERARY_GENERATION_ERROR,
        ) from e


@app.post(
    "/api/answer-query",
    response_model=QueryResponse,
    status_code=status.HTTP_200_OK,
    summary="Answer User Query",
    description="Process user queries with conversation history context.",
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request data"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
)
async def answer_query(request: QueryRequest) -> QueryResponse:
    """Answer a user's query with conversation history context.

    This endpoint processes user queries and maintains conversation context
    through chat history using Google Gemini AI as a Bali travel assistant.

    Args: request: QueryRequest containing the user's query and chat history

    Returns: QueryResponse: AI-generated response to the query

    Raises: ValueError: If input validation fails
        HTTPException: 400 for invalid input, 500 for server errors
    """
    logger.info(
        f"Processing query (length: {len(request.query)}, "
        f"history: {len(request.history)} messages)",
    )

    try:
        # Process query using AI
        answer_content = await process_query_with_ai(request)

        logger.info("Successfully processed user query")
        return QueryResponse(answer=answer_content)

    except ValueError as e:
        logger.warning(f"Invalid input for query processing: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.exception("Unexpected error in query processing")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=QUERY_PROCESSING_ERROR,
        ) from e


@app.post(
    "/api/handle-contact-inquiry",
    response_model=ContactAnalysisResponse,
    status_code=status.HTTP_200_OK,
    summary="Analyze Contact Inquiry",
    description="Analyze and categorize contact form submissions.",
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request data"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
)
async def handle_contact_inquiry(
    request: ContactInquiryRequest,
) -> ContactAnalysisResponse:
    """Analyze a contact inquiry to categorize it and provide insights.

    This endpoint processes contact form submissions and provides analysis
    including categorization and suggested responses.

    Args: request: ContactInquiryRequest containing name, email, and message

    Returns: ContactAnalysisResponse: Analysis of the contact inquiry

    Raises: ValueError: If input validation fails
        HTTPException: 400 for invalid input, 500 for server errors
    """
    logger.info(
        f"Processing contact inquiry from {request.name} "
        f"(message length: {len(request.message)})",
    )

    try:
        # Analyze contact inquiry using AI
        analysis_result = await analyze_contact_with_ai(request)

        logger.info(f"Successfully analyzed contact inquiry from {request.name}")
        return ContactAnalysisResponse(analysis=analysis_result)

    except ValueError as e:
        logger.warning("Invalid input for contact inquiry analysis")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.exception("Unexpected error in contact inquiry analysis")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=CONTACT_INQUIRY_ERROR,
        ) from e


# --- Error Handling Middleware ---


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions with structured error responses.

    Args: request: The incoming request that caused the HTTP exception.
        exc: The HTTPException that was raised.

    Returns: A JSONResponse with HTTP error details.
    """
    logger.warning(
        f"HTTP {exc.status_code} error for {request.method} {request.url.path}: {exc.detail}",
    )

    error_response: dict[str, Any] = create_error_response(
        message=f"HTTP {exc.status_code} Error",
        detail=exc.detail,
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response,
    )


@app.exception_handler(ValueError)
async def validation_exception_handler(
    request: Request,
    exc: ValueError,
) -> JSONResponse:
    """Handle validation errors with detailed error messages.

    Args: request: The incoming request that caused the validation error.
        exc: The ValueError that was raised.

    Returns: A JSONResponse with validation error details.
    """
    logger.warning(f"Validation error for {request.url.path}: {exc}")

    error_response: dict[str, Any] = create_error_response(
        message="Validation Error",
        detail=str(exc),
    )

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=error_response,
    )


@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions with generic error response.

    Args: request: The incoming request that caused the exception.
        exc: The exception that was raised.

    Returns: A JSONResponse with error details.
    """
    logger.exception(f"Unhandled exception for {request.method} {request.url.path}")

    error_response: dict[str, Any] = create_error_response(
        message="Internal Server Error",
        detail=DEFAULT_ERROR_MESSAGE,
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response,
    )


# --- Health Check Endpoints ---


@app.get("/health", response_model=dict[str, str])
async def health_check() -> dict[str, str]:
    """Detailed health check endpoint for monitoring.

    Returns: A dictionary containing health status information.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": "1.0.0",
    }


@app.get("/ready", response_model=dict[str, str])
async def readiness_check() -> dict[str, str]:
    """Readiness check for container orchestration.

    Returns: A dictionary containing readiness status information.
    """
    # Add checks for external dependencies here
    # e.g., database connections, API availability
    return {
        "status": "ready",
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }
