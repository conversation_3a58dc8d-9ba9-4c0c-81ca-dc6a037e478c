# BaliBlissed AI Backend API Documentation

This document provides comprehensive guidance on how to integrate and use the BaliBlissed AI Backend API endpoints in your Next.js project.

## 🚀 Getting Started

### Base URL

- **Development**: `http://localhost:8000`
- **Production**: Set via `PRODUCTION_FRONTEND_URL` environment variable

### Authentication

Currently, no authentication is required for the API endpoints.

### Content Type

All POST requests should use `Content-Type: application/json`

## 📋 Available Endpoints

### 1. Health Check Endpoints

#### GET `/`

Basic health check to verify server status.

**Response:**

```json
{
  "status": "ok",
  "message": "Welcome to the BaliBlissed AI Backend!"
}
```

#### GET `/health`

Detailed health check for monitoring.

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "version": "1.0.0"
}
```

#### GET `/ready`

Readiness check for container orchestration.

**Response:**

```json
{
  "status": "ready",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 2. AI-Powered Endpoints

#### POST `/api/suggest-itinerary`

Generate a personalized travel itinerary based on destination, duration, and interests.

**Request Body:**

```typescript
interface ItineraryRequest {
  destination: string;    // 1-100 characters, e.g., "Bali, Indonesia"
  duration: number;       // 1-365 days
  interests: string[];    // Max 20 items, e.g., ["beaches", "culture", "food"]
}
```

**Example Request:**

```json
{
  "destination": "Bali, Indonesia",
  "duration": 7,
  "interests": ["beaches", "temples", "local cuisine", "adventure sports"]
}
```

**Response:**

```typescript
interface ItineraryResponse {
  itinerary: string;  // Detailed AI-generated itinerary
}
```

#### POST `/api/answer-query`

Process user queries with conversation history context.

**Request Body:**

```typescript
interface ChatMessage {
  role: string;                    // "user" or "assistant"
  parts: Array<{text: string}>;   // Message content
}

interface QueryRequest {
  query: string;              // 1-1000 characters
  history: ChatMessage[];     // Max 50 messages
}
```

**Example Request:**

```json
{
  "query": "What are the best beaches in Bali for surfing?",
  "history": [
    {
      "role": "user",
      "parts": [{"text": "Hello, I'm planning a trip to Bali"}]
    },
    {
      "role": "assistant", 
      "parts": [{"text": "Great! I'd be happy to help you plan your Bali trip."}]
    }
  ]
}
```

**Response:**

```typescript
interface QueryResponse {
  answer: string;  // AI-generated response
}
```

#### POST `/api/handle-contact-inquiry`

Analyze and categorize contact form submissions.

**Request Body:**

```typescript
interface ContactInquiryRequest {
  name: string;     // 1-100 characters
  email: string;    // Valid email address
  message: string;  // 10-2000 characters
}
```

**Example Request:**

```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "message": "I'm interested in booking a 5-day cultural tour of Bali. Can you provide more information about available packages and pricing?"
}
```

**Response:**

```typescript
interface ContactAnalysisResponse {
  analysis: {
    category: string;           // e.g., "Booking Inquiry"
    sentiment: string;          // "Positive", "Neutral", or "Negative"
    urgency: string;           // "High", "Medium", or "Low"
    suggested_response: string; // Professional response template
  }
}
```

## 🔧 Next.js Integration Examples

### 1. API Client Setup

Create a utility file for API calls:

```typescript
// lib/api-client.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public detail?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new APIError(
      errorData.error || 'API request failed',
      response.status,
      errorData.detail
    );
  }

  return response.json();
}

export { apiRequest };
```

### 2. Itinerary Generation Hook

```typescript
// hooks/useItinerary.ts
import { useState } from 'react';
import { apiRequest, APIError } from '@/lib/api-client';

interface ItineraryRequest {
  destination: string;
  duration: number;
  interests: string[];
}

interface ItineraryResponse {
  itinerary: string;
}

export function useItinerary() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateItinerary = async (request: ItineraryRequest): Promise<string | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiRequest<ItineraryResponse>(
        '/api/suggest-itinerary',
        {
          method: 'POST',
          body: JSON.stringify(request),
        }
      );
      
      return response.itinerary;
    } catch (err) {
      const errorMessage = err instanceof APIError 
        ? err.message 
        : 'Failed to generate itinerary';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { generateItinerary, loading, error };
}
```

### 3. Chat Query Hook

```typescript
// hooks/useChat.ts
import { useState } from 'react';
import { apiRequest, APIError } from '@/lib/api-client';

interface ChatMessage {
  role: string;
  parts: Array<{text: string}>;
}

interface QueryRequest {
  query: string;
  history: ChatMessage[];
}

interface QueryResponse {
  answer: string;
}

export function useChat() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [history, setHistory] = useState<ChatMessage[]>([]);

  const sendQuery = async (query: string): Promise<string | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiRequest<QueryResponse>(
        '/api/answer-query',
        {
          method: 'POST',
          body: JSON.stringify({ query, history }),
        }
      );

      // Update conversation history
      const userMessage: ChatMessage = {
        role: 'user',
        parts: [{ text: query }]
      };

      const assistantMessage: ChatMessage = {
        role: 'assistant',
        parts: [{ text: response.answer }]
      };

      setHistory(prev => [...prev, userMessage, assistantMessage]);

      return response.answer;
    } catch (err) {
      const errorMessage = err instanceof APIError
        ? err.message
        : 'Failed to process query';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const clearHistory = () => setHistory([]);

  return { sendQuery, loading, error, history, clearHistory };
}
```

### 4. Contact Form Hook

```typescript
// hooks/useContactForm.ts
import { useState } from 'react';
import { apiRequest, APIError } from '@/lib/api-client';

interface ContactInquiryRequest {
  name: string;
  email: string;
  message: string;
}

interface ContactAnalysisResponse {
  analysis: {
    category: string;
    sentiment: string;
    urgency: string;
    suggested_response: string;
  };
}

export function useContactForm() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const submitInquiry = async (
    request: ContactInquiryRequest
  ): Promise<ContactAnalysisResponse['analysis'] | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiRequest<ContactAnalysisResponse>(
        '/api/handle-contact-inquiry',
        {
          method: 'POST',
          body: JSON.stringify(request),
        }
      );

      return response.analysis;
    } catch (err) {
      const errorMessage = err instanceof APIError
        ? err.message
        : 'Failed to submit inquiry';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { submitInquiry, loading, error };
}
```

## 🎯 React Component Examples

### Itinerary Generator Component

```tsx
// components/ItineraryGenerator.tsx
'use client';

import { useState } from 'react';
import { useItinerary } from '@/hooks/useItinerary';

export default function ItineraryGenerator() {
  const [destination, setDestination] = useState('');
  const [duration, setDuration] = useState(7);
  const [interests, setInterests] = useState<string[]>([]);
  const [itinerary, setItinerary] = useState<string | null>(null);

  const { generateItinerary, loading, error } = useItinerary();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const result = await generateItinerary({
      destination,
      duration,
      interests,
    });

    if (result) {
      setItinerary(result);
    }
  };

  const handleInterestToggle = (interest: string) => {
    setInterests(prev =>
      prev.includes(interest)
        ? prev.filter(i => i !== interest)
        : [...prev, interest]
    );
  };

  const availableInterests = [
    'beaches', 'temples', 'culture', 'food', 'adventure',
    'nightlife', 'nature', 'shopping', 'wellness', 'photography'
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Generate Your Bali Itinerary</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">
            Destination
          </label>
          <input
            type="text"
            value={destination}
            onChange={(e) => setDestination(e.target.value)}
            placeholder="e.g., Bali, Indonesia"
            className="w-full p-3 border rounded-lg"
            required
            maxLength={100}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Duration (days)
          </label>
          <input
            type="number"
            value={duration}
            onChange={(e) => setDuration(Number(e.target.value))}
            min={1}
            max={365}
            className="w-full p-3 border rounded-lg"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Interests (select up to 20)
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {availableInterests.map((interest) => (
              <button
                key={interest}
                type="button"
                onClick={() => handleInterestToggle(interest)}
                className={`p-2 rounded-lg border text-sm ${
                  interests.includes(interest)
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                }`}
              >
                {interest}
              </button>
            ))}
          </div>
        </div>

        <button
          type="submit"
          disabled={loading || !destination || interests.length === 0}
          className="w-full bg-blue-500 text-white p-3 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Generating...' : 'Generate Itinerary'}
        </button>
      </form>

      {error && (
        <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
          {error}
        </div>
      )}

      {itinerary && (
        <div className="mt-8">
          <h3 className="text-xl font-semibold mb-4">Your Personalized Itinerary</h3>
          <div className="bg-gray-50 p-6 rounded-lg">
            <pre className="whitespace-pre-wrap text-sm">{itinerary}</pre>
          </div>
        </div>
      )}
    </div>
  );
}
```

### Chat Component

```tsx
// components/ChatBot.tsx
'use client';

import { useState } from 'react';
import { useChat } from '@/hooks/useChat';

export default function ChatBot() {
  const [input, setInput] = useState('');
  const { sendQuery, loading, error, history, clearHistory } = useChat();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const query = input.trim();
    setInput('');
    await sendQuery(query);
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Bali Travel Assistant</h2>
        {history.length > 0 && (
          <button
            onClick={clearHistory}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Clear Chat
          </button>
        )}
      </div>

      <div className="bg-gray-50 rounded-lg p-4 h-96 overflow-y-auto mb-4">
        {history.length === 0 ? (
          <p className="text-gray-500 text-center">
            Ask me anything about Bali travel!
          </p>
        ) : (
          <div className="space-y-4">
            {history.map((message, index) => (
              <div
                key={index}
                className={`flex ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-800 border'
                  }`}
                >
                  {message.parts[0]?.text}
                </div>
              </div>
            ))}
          </div>
        )}

        {loading && (
          <div className="flex justify-start">
            <div className="bg-white text-gray-800 border px-4 py-2 rounded-lg">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              </div>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-sm">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex space-x-2">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Ask about Bali attractions, culture, food..."
          className="flex-1 p-3 border rounded-lg"
          maxLength={1000}
          disabled={loading}
        />
        <button
          type="submit"
          disabled={loading || !input.trim()}
          className="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Send
        </button>
      </form>
    </div>
  );
}
```

## 🛠️ Error Handling

### Error Response Format

All API endpoints return standardized error responses:

```typescript
interface ErrorResponse {
  error: string;        // Main error message
  detail?: string;      // Additional error details
  timestamp: string;    // ISO timestamp
}
```

### Common HTTP Status Codes

- **200**: Success
- **400**: Bad Request (validation errors)
- **500**: Internal Server Error

### Error Handling Best Practices

```typescript
// utils/error-handler.ts
import { APIError } from '@/lib/api-client';

export function handleAPIError(error: unknown): string {
  if (error instanceof APIError) {
    switch (error.status) {
      case 400:
        return `Invalid input: ${error.detail || error.message}`;
      case 500:
        return 'Server error. Please try again later.';
      default:
        return error.message;
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unexpected error occurred';
}
```

## 🔒 Security Considerations

### CORS Configuration

The backend is configured to accept requests from:

- `http://localhost:3000` (Next.js development)
- `http://127.0.0.1:3000`
- Production URL (via `PRODUCTION_FRONTEND_URL` environment variable)

### Input Validation

All endpoints include comprehensive input validation:

- String length limits
- Email format validation
- Sanitization of potentially harmful characters
- Rate limiting (100 requests per hour per IP)

### Security Headers

The API includes security headers:

- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security`
- `Referrer-Policy: strict-origin-when-cross-origin`

## 🚀 Deployment

### Environment Variables

Create a `.env.local` file in your Next.js project:

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000

# For production
# NEXT_PUBLIC_API_URL=https://your-api-domain.com
```

### Backend Environment Variables

The backend requires:

```bash
# Required
GEMINI_API_KEY=your_google_gemini_api_key

# Optional
ENVIRONMENT=production
PRODUCTION_FRONTEND_URL=https://your-frontend-domain.com
LOG_TO_FILE=true
```

### Running the Backend

```bash
# Install dependencies
cd backend
pip install -r requirements.txt

# Run development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Run production server
uvicorn main:app --host 0.0.0.0 --port 8000
```

## 📝 TypeScript Definitions

For better type safety, create a types file:

```typescript
// types/api.ts
export interface ItineraryRequest {
  destination: string;
  duration: number;
  interests: string[];
}

export interface ItineraryResponse {
  itinerary: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  parts: Array<{ text: string }>;
}

export interface QueryRequest {
  query: string;
  history: ChatMessage[];
}

export interface QueryResponse {
  answer: string;
}

export interface ContactInquiryRequest {
  name: string;
  email: string;
  message: string;
}

export interface ContactAnalysisResponse {
  analysis: {
    category: string;
    sentiment: 'Positive' | 'Neutral' | 'Negative';
    urgency: 'High' | 'Medium' | 'Low';
    suggested_response: string;
  };
}

export interface ErrorResponse {
  error: string;
  detail?: string;
  timestamp: string;
}
```

## 🎉 Conclusion

This API provides powerful AI-driven features for your Bali travel application. The endpoints are designed to be simple to integrate while providing comprehensive functionality for itinerary generation, user queries, and contact form analysis.

For additional support or questions, refer to the backend logs or check the `/health` endpoint for system status.
